using System.Security.Claims;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using Microsoft.EntityFrameworkCore.ValueGeneration;
using Microsoft.Extensions.Logging;
using ProScoring.Domain.Entities;
using ProScoring.Domain.Entities.EntityInterfaces;
using ProScoring.Domain.Entities.RegattaEntities;
using ProScoring.Infrastructure.Authorization.Entities;
using ProScoring.Infrastructure.ServiceInterfaces;
using ProScoring.Infrastructure.Services;

namespace ProScoring.Infrastructure.Database;

/// <summary>
/// Represents the database context for the ProScoring application.
/// </summary>
public class ApplicationDbContext : IdentityDbContext<ApplicationUser>, IApplicationDbContext
{
    #region constants

    public const string NULL_USER_ID = "U000000000";

    #endregion constants

    #region fields

    private readonly AuthenticationStateProvider? _authenticationStateProvider;
    private readonly IDateTimeOffsetProvider _dateTimeOffsetProvider;
    private readonly ValueGenerator<string> _idValueGenerator;
    private readonly ILogger<ApplicationDbContext> _logger;

    #endregion fields

    #region constructors

    public ApplicationDbContext(
        DbContextOptions<ApplicationDbContext> options,
        AuthenticationStateProvider authenticationStateProvider,
        IValueGenerator idValueGenerator,
        ILogger<ApplicationDbContext> logger,
        IDateTimeOffsetProvider dateTimeOffsetProvider = null!
    )
        : this(options, idValueGenerator, logger)
    {
        _authenticationStateProvider = authenticationStateProvider;
        _dateTimeOffsetProvider = dateTimeOffsetProvider ?? new DateTimeOffsetProviderCpuTime();
    }

    protected internal ApplicationDbContext(
        DbContextOptions<ApplicationDbContext> options,
        IValueGenerator idValueGenerator,
        ILogger<ApplicationDbContext> logger
    )
        : base(options)
    {
        // Need to be sure that this extends the base class
        _idValueGenerator =
            idValueGenerator as ValueGenerator<string>
            ?? throw new ArgumentException(
                "{nameof(idValueGenerator)} must be of type ValueGenerator<string>",
                nameof(idValueGenerator)
            );

        OrganizingAuthorities = Set<OrganizingAuthority>();
        Files = Set<FileRecord>();
        //ImageMetadata = Set<ImageMetadata>();
        AuthActions = Set<AuthAction>();
        UserAuthActions = Set<UserAuthAction>();
        ActionHierarchies = Set<ActionHierarchy>();
        TargetTypes = Set<TargetType>();
        Regattas = Set<Regatta>();
        RegattaExternalLinks = Set<RegattaExternalLink>();
        RegattaBoats = Set<RegattaBoat>();
        RegattaClasses = Set<RegattaClass>();
        RegattaCompetitors = Set<RegattaCompetitor>();
        RegattaFleets = Set<RegattaFleet>();
        RegattaRatings = Set<RegattaRating>();
        RegattaRatingValues = Set<RegattaRatingValue>();
        OverridePermissions = Set<OverridePermission>();
        _dateTimeOffsetProvider = new DateTimeOffsetProviderCpuTime();
        _logger = logger;
    }

    #endregion constructors

    #region properties
    // Note: When adding a new DbSet, remember to add it
    // to the MigrationGenerationDbContexts.cs file.
    // I tried to get them to work with a common base class,
    // but had a problem with the options argument to the constructor.
    //  file:///c:/_dev/ProScoring/ProScoringNet9/ProScoring.Infrastructure/Database/MigrationGenerationDbContexts.cs

    public virtual DbSet<ActionHierarchy> ActionHierarchies { get; set; } = null!;
    public virtual DbSet<AuthAction> AuthActions { get; }
    public virtual DbSet<FileRecord> Files { get; }
    public virtual DbSet<OrganizingAuthority> OrganizingAuthorities { get; }
    public virtual DbSet<Regatta> Regattas { get; }
    public virtual DbSet<RegattaBoat> RegattaBoats { get; }
    public virtual DbSet<RegattaClass> RegattaClasses { get; }
    public virtual DbSet<RegattaCompetitor> RegattaCompetitors { get; }
    public virtual DbSet<RegattaExternalLink> RegattaExternalLinks { get; }
    public virtual DbSet<RegattaFleet> RegattaFleets { get; }
    public virtual DbSet<RegattaRating> RegattaRatings { get; }
    public virtual DbSet<RegattaRatingValue> RegattaRatingValues { get; }
    public virtual DbSet<TargetType> TargetTypes { get; }
    public virtual DbSet<UserAuthAction> UserAuthActions { get; }

    /// <summary>
    /// Gets or sets the DbSet for Override Permissions.
    /// </summary>
    public virtual DbSet<OverridePermission> OverridePermissions { get; set; } = null!;

    #endregion properties

    #region methods

    /// <summary>
    /// Saves all changes made in this context to the database.
    /// </summary>
    /// <returns>The number of state entries written to the database.</returns>
    /// <exception cref="DbUpdateException">An error occurred while saving to the database.</exception>
    /// <exception cref="DbUpdateConcurrencyException">A concurrency violation is encountered.</exception>
    public override int SaveChanges()
    {
        try
        {
            _logger.LogDebug("Saving changes to database");
            DoChangeTrackingAsync().GetAwaiter().GetResult();
            var result = base.SaveChanges();
            _logger.LogDebug("Successfully saved {Count} changes to database", result);
            return result;
        }
        catch (DbUpdateConcurrencyException ex)
        {
            _logger.LogError(ex, "Concurrency error occurred during SaveChanges");
            throw;
        }
        catch (DbUpdateException ex)
        {
            _logger.LogError(ex, "Database error occurred during SaveChanges");
            throw;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error occurred during SaveChanges");
            throw;
        }
    }

    /// <summary>
    /// Saves all changes made in this context to the database asynchronously.
    /// </summary>
    /// <param name="cancellationToken">A token to observe while waiting for the task to complete.</param>
    /// <returns>A task that represents the asynchronous save operation. The task result contains the number of state entries written to the database.</returns>
    /// <exception cref="DbUpdateException">An error occurred while saving to the database.</exception>
    /// <exception cref="DbUpdateConcurrencyException">A concurrency violation is encountered.</exception>
    /// <exception cref="OperationCanceledException">If the cancellation token is triggered.</exception>
    public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Saving changes to database asynchronously");
            cancellationToken.ThrowIfCancellationRequested();
            await DoChangeTrackingAsync();
            var result = await base.SaveChangesAsync(cancellationToken);
            _logger.LogDebug("Successfully saved {Count} changes to database asynchronously", result);
            return result;
        }
        catch (DbUpdateConcurrencyException ex)
        {
            _logger.LogError(ex, "Concurrency error occurred during SaveChangesAsync");
            throw;
        }
        catch (DbUpdateException ex)
        {
            _logger.LogError(ex, "Database error occurred during SaveChangesAsync");
            throw;
        }
        catch (OperationCanceledException ex) when (cancellationToken.IsCancellationRequested)
        {
            _logger.LogInformation(ex, "SaveChangesAsync operation was canceled");
            throw;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error occurred during SaveChangesAsync");
            throw;
        }
    }

    /// <summary>
    /// Configures the database context options.
    /// </summary>
    /// <param name="optionsBuilder">The builder being used to configure options.</param>
    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
    {
        optionsBuilder.UseLazyLoadingProxies();
        base.OnConfiguring(optionsBuilder);
    }

    /// <summary>
    /// Configures the model that was discovered by convention from the entity types
    /// exposed in <see cref="DbSet{TEntity}"/> properties on this context.
    /// </summary>
    /// <param name="builder">The builder being used to configure the model.</param>
    protected override void OnModelCreating(ModelBuilder builder)
    {
        base.OnModelCreating(builder);

        // Process each entity type defined in the model
        foreach (var entityType in builder.Model.GetEntityTypes())
        {
            var entity = builder.Entity(entityType.ClrType);
            var typedEntityBuilder = builder.CreateTypedEntityBuilder(entityType.ClrType);

            // Configure auto-generated IDs if the entity implements IHasAutoInsertedId
            entityType.ConfigureAutoGeneratedId(entity, _idValueGenerator);

            // Configure compound keys if the entity implements IHasCompoundKey<T>
            entityType.ConfigureCompoundKeys(typedEntityBuilder);

            // Configure foreign keys if the entity implements IHasForeignKeyConfiguration<T>
            entityType.ConfigureForeignKeys(typedEntityBuilder);

            // Configure seed data if the entity implements IHasInitialSeedData<T>
            entityType.ConfigureSeedData(typedEntityBuilder);
        }

        // Configuration for OverridePermission
        var overridePermissionEntity = builder.Entity<OverridePermission>();

        // Primary Key is already defined by [Key] attribute on Id.

        // Relationship to ApplicationUser for UserId (the user whose permission is overridden)
        overridePermissionEntity
            .HasOne(op => op.User)
            .WithMany() // Assuming ApplicationUser does not have a navigation collection for these overrides
            .HasForeignKey(op => op.UserId)
            .IsRequired() // UserId is marked as [Required]
            .OnDelete(DeleteBehavior.Cascade); // Cascade delete if the User is deleted. Adjust if different behavior is needed.

        // Relationship to ApplicationUser for CreatedByUserId (the user who created the override)
        overridePermissionEntity
            .HasOne(op => op.CreatedByUser)
            .WithMany() // Assuming ApplicationUser does not have a navigation collection for this either
            .HasForeignKey(op => op.CreatedByUserId)
            .IsRequired(false) // CreatedByUserId is nullable
            .OnDelete(DeleteBehavior.SetNull); // If the creating user is deleted, set CreatedByUserId to null.
        // This is suitable for nullable foreign keys.
    }

    /// <summary>
    /// Applies change tracking to entities that implement IHasLastChangeTracking.
    /// </summary>
    /// <returns>A task representing the asynchronous operation.</returns>
    private async Task DoChangeTrackingAsync()
    {
        try
        {
            var entries = GetChangeTrackingEntries();
            if (!entries.Any())
            {
                _logger.LogTrace("No entities found requiring change tracking");
                return;
            }

            _logger.LogDebug("Applying change tracking to {Count} entities", entries.Count());

            string? id = null;
            try
            {
                id = await GetUserIdAsync();
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Could not determine current user ID, using NULL_USER_ID as fallback");
                id = NULL_USER_ID;
            }

            foreach (var entry in entries)
            {
                try
                {
                    if (entry.Entity is not IHasLastChangeTracking
                    // || (
                    //     entry.State == EntityState.Modified
                    //     && !entry
                    //         .Properties.Where(p => p.Metadata.Name != "ConcurrencyStamp")
                    //         .Any(p => p.IsModified && !Equals(p.CurrentValue, p.OriginalValue))
                    // )
                    )
                    {
                        continue;
                    }
                    UpdateChangeTrackingFields(entry, id);
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "Error updating change tracking fields for entity {EntityType}",
                        entry.Entity.GetType().Name
                    );
                    throw;
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in DoChangeTrackingAsync");
            throw;
        }
    }

    /// <summary>
    /// Gets entities that implement IHasLastChangeTracking and have been added or modified.
    /// </summary>
    /// <returns>Collection of entity entries that should have change tracking applied.</returns>
    private IEnumerable<EntityEntry> GetChangeTrackingEntries()
    {
        try
        {
            var entries = ChangeTracker
                .Entries()
                .Where(e =>
                    e.Entity is IHasLastChangeTracking
                    && (e.State == EntityState.Added || e.State == EntityState.Modified)
                );

            _logger.LogTrace("Found {Count} entities for change tracking", entries.Count());
            return entries;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting change tracking entries");
            throw;
        }
    }

    /// <summary>
    /// Gets the ID of the currently authenticated user, if available.
    /// </summary>
    /// <returns>The user ID or null if not available.</returns>
    private async Task<string?> GetUserIdAsync()
    {
        if (_authenticationStateProvider == null)
        {
            _logger.LogDebug("AuthenticationStateProvider is null, cannot determine current user");
            return null;
        }

        try
        {
            var authState = await _authenticationStateProvider.GetAuthenticationStateAsync();
            var user = authState?.User;
            var userId = user?.FindFirst(ClaimTypes.NameIdentifier)?.Value;

            if (string.IsNullOrEmpty(userId))
            {
                _logger.LogDebug("User identifier claim not found or empty");
            }
            else
            {
                _logger.LogTrace("Retrieved user ID: {UserId}", userId);
            }

            return userId;
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogError(ex, "Failed to get authentication state");
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error getting user ID from authentication state");
            return null;
        }
    }

    /// <summary>
    /// Updates the change tracking fields of an entity.
    /// </summary>
    /// <param name="entry">The entity entry to update.</param>
    /// <param name="id">The identifier of the user making the change, or null if not available.</param>
    /// <remarks>
    /// If the entity implements the <see cref="IHasLastChangeTracking"/> interface, this method will update the
    /// <see cref="IHasLastChangeTracking.CreatedAt"/>, <see cref="IHasLastChangeTracking.CreatedById"/>,
    /// <see cref="IHasLastChangeTracking.UpdatedAt"/>, and <see cref="IHasLastChangeTracking.UpdatedById"/> properties
    /// based on the entity state and provided user identifier.
    /// </remarks>
    private void UpdateChangeTrackingFields(EntityEntry entry, string? id)
    {
        if (entry.Entity is not IHasLastChangeTracking changeTrackEntity)
        {
            _logger.LogTrace("Entity is not IHasLastChangeTracking, skipping change tracking");
            return;
        }

        try
        {
            // Special case for ApplicationUser, because it is created before the user is logged in.
            if (entry.Entity is ApplicationUser user && id == null)
            {
                id = user.Id;
                _logger.LogDebug("Using ApplicationUser's own ID for change tracking: {UserId}", id);
            }

            id ??= NULL_USER_ID;

            var now = _dateTimeOffsetProvider.UtcNow;
            if (entry.State == EntityState.Added)
            {
                _logger.LogDebug(
                    "Setting creation tracking fields for new entity {EntityType}",
                    entry.Entity.GetType().Name
                );
                changeTrackEntity.CreatedAt = now;
                changeTrackEntity.CreatedById = id;
            }

            changeTrackEntity.UpdatedAt = now;
            changeTrackEntity.UpdatedById = id;
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Error updating change tracking fields for entity {EntityType}",
                entry.Entity.GetType().Name
            );
            throw;
        }
    }

    #endregion methods
}
